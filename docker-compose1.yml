version: '3.8'

services:
  smart-card-mcp:
    image: smart-card-mcp:latest
    build:
      context: .
      dockerfile: Dockerfile
    container_name: smart-card-mcp-server-dstaff
    restart: unless-stopped
    ports:
      - "48188:48188"  # SSE port
      - "48189:48189"  # HTTP port
      - "49183:49083"  # Health check port
    environment:
      # Preferred: ARK API configuration (DeepSeek V3)
      - ARK_API_KEY=576a03b3-c58c-4f73-9725-7d3ba37945ce
      - ARK_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
      - ARK_MODEL=deepseek-v3-1-250821

      # Fallback: OpenAI API configuration
      - OPENAI_API_KEY=sk-iuEJ0PFBn0va5K9T7eA3FaF0D4814b8c8e70AbC9146dDdA3
      - OPENAI_BASE_URL=https://oneapi.wetolink.com/v1
      - OPENAI_MODEL=gpt-4.1

      # Required: Jina API for web fetching
      - JINA_API_KEY=jina_8d6e6efc4ddd444c889ed76c9562d252FLKbD14FzVMzE8SdnKFDQ7_uZHxk
      - JINA_API_URL=https://r.jina.ai/

      # Optional: Log level (DEBUG, INFO, WARNING, ERROR)
      - LOG_LEVEL=INFO

      # Optional: SSE Access Key for authentication
      - SSE_ACCESS_KEY=smart-card-mcp-237c4cfcd7a689e6b6dcd743b9241e92

      # MCP Server Configuration
      - MCP_PORTS=48188,48189
      - MCP_BASE_URL=https://smartcard-dstaff.wetolink.com
      - MCP_TRANSPORT=mixed

      # Optional: Enable MCP debug mode for detailed logging
      - MCP_DEBUG=false

      # Output and templates directories
      - OUTPUT_DIR=/app/output
      - TEMPLATES_DIR=/app/templates

      - DSTAFF_ENABLED=true
      - DSTAFF_ENDPOINT_URL=https://dstaff.dbappsecurity.com.cn
      - DSTAFF_USE_OFFICIAL_AUTH=true
    volumes:
      # Optional: Mount logs directory for persistent logging
      - ./logs:/app/logs
      # Mount data directory for any persistent data
      - ./data:/app/data
      # Mount output directory for generated cards
      - ./output:/app/output
      # Optional: Mount custom templates
      - ./templates:/app/templates:ro
    networks:
      - smart-card-network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:49083/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "com.smart-card.service=mcp-server"
      - "com.smart-card.version=1.0.0"
      - "com.smart-card.ports=48083,48084"
      - "com.smart-card.description=Smart Card MCP Server for text summarization and card generation"

networks:
  smart-card-network:
    driver: bridge
    name: smart-card-network

volumes:
  logs:
    driver: local
  data:
    driver: local
  output:
    driver: local