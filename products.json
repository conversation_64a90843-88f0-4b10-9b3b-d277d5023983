{"version": "V1.0", "name": "mcp-digital-teacher", "type": "DEFAULT", "base_image": [], "service": {"app": [{"name": "mcp-smart-card", "version": "v1", "build": {"image": "mcp-smart-card:v1", "externalPortList": [{"externalPort": 48188, "internalPort": 48188}, {"externalPort": 48189, "internalPort": 48189}, {"externalPort": 49183, "internalPort": 49083}], "values": {"replicas": 1, "expose": [48188, 48189, 49083], "env": [{"name": "ARK_API_KEY", "value": "576a03b3-c58c-4f73-9725-7d3ba37945ce"}, {"name": "ARK_BASE_URL", "value": "https://ark.cn-beijing.volces.com/api/v3"}, {"name": "ARK_MODEL", "value": "deepseek-v3-1-250821"}, {"name": "OPENAI_API_KEY", "value": "sk-iuEJ0PFBn0va5K9T7eA3FaF0D4814b8c8e70AbC9146dDdA3"}, {"name": "OPENAI_BASE_URL", "value": "https://oneapi.wetolink.com/v1"}, {"name": "OPENAI_MODEL", "value": "gpt-4.1"}, {"name": "JINA_API_KEY", "value": "jina_8d6e6efc4ddd444c889ed76c9562d252FLKbD14FzVMzE8SdnKFDQ7_uZHxk"}, {"name": "JINA_API_URL", "value": "https://r.jina.ai/"}, {"name": "LOG_LEVEL", "value": "INFO"}, {"name": "SSE_ACCESS_KEY", "value": "smart-card-mcp-237c4cfcd7a689e6b6dcd743b9241e92"}, {"name": "MCP_PORTS", "value": "48188,48189"}, {"name": "MCP_BASE_URL", "value": "https://smartcard-dstaff.wetolink.com"}, {"name": "MCP_TRANSPORT", "value": "mixed"}, {"name": "MCP_DEBUG", "value": "false"}, {"name": "OUTPUT_DIR", "value": "/app/output"}, {"name": "TEMPLATES_DIR", "value": "/app/templates"}, {"name": "DSTAFF_ENABLED", "value": "true"}, {"name": "DSTAFF_ENDPOINT_URL", "value": "https://dstaff.dbappsecurity.com.cn"}, {"name": "DSTAFF_USE_OFFICIAL_AUTH", "value": "true"}], "volumes": [{"name": "smart-card-logs", "mountPath": "/app/logs"}, {"name": "smart-card-data", "mountPath": "/app/data"}, {"name": "smart-card-output", "mountPath": "/app/output"}, {"name": "smart-card-templates", "mountPath": "/app/templates", "readOnly": true}], "healthcheck": [{"name": "test", "value": ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:49083/health || exit 1"]}, {"name": "interval", "value": 30}, {"name": "timeout", "value": 10}, {"name": "retries", "value": 3}, {"name": "startPeriod", "value": 40}], "labels": [{"name": "com.smart-card.service", "value": "mcp-server"}, {"name": "com.smart-card.version", "value": "1.0.0"}, {"name": "com.smart-card.ports", "value": "48188,48189"}]}}}, {"name": "chatppt-mcp-server", "version": "v1", "build": {"image": "chatppt-mcp-server:v1", "externalPortList": [{"externalPort": 48083, "internalPort": 48083}, {"externalPort": 48084, "internalPort": 48084}], "values": {"replicas": 1, "expose": [48083, 48084], "env": [{"name": "API_PPT_KEY", "value": "Yoo-AI.5134.6WG2uXnSG9FMu7uqXrXGX5SJmv6TCADk.z3smK2z7PAZGBJ5EyDJBhNykaxuEScy9/BQSXwQncKdS5fQXwjGAYvey4AHhnVqT5.**********.0"}, {"name": "LOG_LEVEL", "value": "DEBUG"}, {"name": "SSE_ACCESS_KEY", "value": ""}, {"name": "MCP_PORTS", "value": "48083,48084"}, {"name": "API_BASE", "value": "https://saas.api.yoo-ai.com"}, {"name": "MCP_TRANSPORT", "value": "mixed"}, {"name": "MCP_BASE_URL", "value": "http://localhost:48083"}, {"name": "DSTAFF_ENABLED", "value": "true"}, {"name": "DSTAFF_ENDPOINT_URL", "value": "https://dstaff.dbappsecurity.com.cn"}, {"name": "DSTAFF_USE_OFFICIAL_AUTH", "value": "true"}, {"name": "DSTAFF_TOKEN", "value": ""}, {"name": "DSTAFF_TASK_ID", "value": ""}, {"name": "MCP_DEBUG", "value": "false"}], "volumes": [{"name": "chatppt-logs", "mountPath": "/app/logs"}, {"name": "chatppt-data", "mountPath": "/app/data"}], "healthcheck": [{"name": "test", "value": ["CMD-SHELL", "curl -f http://localhost:48083/health || curl -f http://localhost:48084/health"]}, {"name": "interval", "value": 30}, {"name": "timeout", "value": 10}, {}], "label": [{"name": "com.chatppt.service", "value": "mcp-server"}, {"name": "com.chatppt.version", "value": "1.0.0"}, {"name": "com.chatppt.ports", "value": "48083,48084"}]}}}, {"name": "mcp-pipeline-server", "version": "v1", "build": {"image": "ppt-narrator-mcp-pipeline-server:v1", "externalPortList": [{"externalPort": 48080, "internalPort": 48088}, {"externalPort": 48081, "internalPort": 48089}, {"externalPort": 48082, "internalPort": 49088}], "values": {"replicas": 1, "expose": [48088, 48089, 49088], "env": [{"name": "PPT_NARRATOR_URL", "value": "http://ppt-narrator:8080"}, {"name": "MCP_PORTS", "value": "48088,48089"}, {"name": "MCP_BASE_URL", "value": "http://localhost:48088"}, {"name": "MCP_TRANSPORT", "value": "mixed"}, {"name": "LOG_LEVEL", "value": "INFO"}, {"name": "OUTPUT_DIR", "value": "output"}, {"name": "TEMPLATES_DIR", "value": "templates"}, {"name": "AUTO_CLEANUP_FILES", "value": "false"}, {"name": "DSTAFF_ENABLED", "value": "true"}, {"name": "DSTAFF_ENDPOINT_URL", "value": "https://dstaff.dbappsecurity.com.cn"}, {"name": "DSTAFF_USE_OFFICIAL_AUTH", "value": "true"}, {"name": "MCP_DEBUG", "value": "false"}], "healthcheck": [{"name": "test", "value": ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:49088/health || exit 1"]}, {"name": "interval", "value": 30}, {"name": "timeout", "value": 10}, {"name": "retries", "value": 3}, {"name": "startPeriod", "value": 30}]}}}, {"name": "ppt-narrator", "version": "v1", "build": {"image": "ppt-narrator-ppt-narrator:v1", "externalPortList": [{"externalPort": 38080, "internalPort": 8080}], "values": {"replicas": 1, "expose": [8080], "env": [{"name": "PORT", "value": "8080"}, {"name": "DATABASE_URL", "value": "*****************************************************/ppt_narrator?sslmode=disable&client_encoding=UTF8"}, {"name": "UPLOAD_DIR", "value": "/app/uploads"}, {"name": "SCREENSHOT_DIR", "value": "/app/screenshots"}, {"name": "VIDEO_DIR", "value": "/app/videos"}, {"name": "TEMP_DIR", "value": "/app/temp"}, {"name": "AI_PROVIDER", "value": "openai"}, {"name": "OPENAI_API_KEY", "value": "sk-iuEJ0PFBn0va5K9T7eA3FaF0D4814b8c8e70AbC9146dDdA3"}, {"name": "OPENAI_MODEL", "value": "gpt-5-chat-latest"}, {"name": "OPENAI_BASE_URL", "value": "https://oneapi.wetolink.com/v1"}, {"name": "MINIMAX_API_KEY", "value": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, {"name": "MINIMAX_GROUP_ID", "value": "1951099282912186398"}, {"name": "MINIMAX_MODEL", "value": "MiniMax-Text-01"}, {"name": "MINIMAX_BASE_URL", "value": "https://api.minimaxi.com/v1/text/chatcompletion_v2"}, {"name": "TTS_PROVIDER", "value": "minimax"}, {"name": "TTS_VOICE", "value": "alloy"}, {"name": "TTS_SPEED", "value": "1.0"}, {"name": "MINIMAX_TTS_API_KEY", "value": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, {"name": "MINIMAX_TTS_GROUP_ID", "value": "1951099282912186398"}, {"name": "MINIMAX_TTS_MODEL", "value": "speech-02-hd"}, {"name": "MINIMAX_TTS_VOICE_ID", "value": "male-qn-qingse"}, {"name": "MINIMAX_TTS_EMOTION", "value": "happy"}, {"name": "EASYVOICE_API_URL", "value": "https://easyvoice.wetolink.com/api/v1/tts/generate"}, {"name": "EASYVOICE_USERNAME", "value": ""}, {"name": "EASYVOICE_PASSWORD", "value": ""}, {"name": "EASYVOICE_VOICE", "value": "zh-CN-YunxiNeural"}, {"name": "EASYVOICE_RATE", "value": "0%"}, {"name": "EASYVOICE_PITCH", "value": "0Hz"}, {"name": "EASYVOICE_VOLUME", "value": "0%"}, {"name": "LIBREOFFICE_PATH", "value": "libreoffice"}, {"name": "FFMPEG_PATH", "value": "ffmpeg"}, {"name": "SYSTEM_PROMPT", "value": ""}, {"name": "NARRATOR_ROLE", "value": "资深教授"}, {"name": "NARRATOR_STYLE", "value": "亲切自然"}, {"name": "TARGET_AUDIENCE", "value": "大学生"}, {"name": "SPEAKING_TONE", "value": "轻松友好"}, {"name": "SPEECH_NATURALNESS", "value": "高度口语化"}, {"name": "MAX_TOKENS", "value": "4000"}, {"name": "TEMPERATURE", "value": "0.7"}, {"name": "SUBTITLE_ENABLED", "value": "false"}, {"name": "SUBTITLE_FONT_SIZE", "value": "24"}, {"name": "SUBTITLE_FONT_COLOR", "value": "#FFFFFF"}, {"name": "SUBTITLE_BACKGROUND_COLOR", "value": ""}, {"name": "SUBTITLE_POSITION", "value": "bottom"}, {"name": "SUBTITLE_FONT_FAMILY", "value": "<PERSON><PERSON>"}, {"name": "SUBTITLE_OUTLINE", "value": "2"}, {"name": "SUBTITLE_SHADOW", "value": "true"}], "healthcheck": [{"name": "test", "value": ["CMD-SHELL", "curl -f http://localhost:8080/health"]}, {"name": "interval", "value": 30}, {"name": "timeout", "value": 10}, {"name": "retries", "value": 3}, {"name": "startPeriod", "value": 60}], "volumes": [{"name": "ppt-uploads", "mountPath": "/app/uploads"}, {"name": "ppt-screenshots", "mountPath": "/app/screenshots"}, {"name": "ppt-videos", "mountPath": "/app/videos"}, {"name": "ppt-temp", "mountPath": "/app/temp"}, {"name": "ppt-audio", "mountPath": "/app/audio"}, {"name": "ppt-work", "mountPath": "/app/work"}]}}}], "common": [], "base": [{"name": "postgres", "version": "pg16", "build": {"image": "pgvector/pgvector:pg16", "values": {"env": [{"name": "POSTGRES_DB", "value": "ppt_narrator"}, {"name": "POSTGRES_USER", "value": "ppt_narrator"}, {"name": "POSTGRES_PASSWORD", "value": "ppt_narrator123"}, {"name": "POSTGRES_INITDB_ARGS", "value": "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"}, {"name": "LC_ALL", "value": "C.UTF-8"}, {"name": "LANG", "value": "C.UTF-8"}], "healthcheck": [{"name": "test", "value": ["CMD-SHELL", "pg_isready -U ppt_narrator -d ppt_narrator"]}, {"name": "interval", "value": 10}, {"name": "timeout", "value": 5}, {"name": "retries", "value": 5}], "volumes": [{"name": "postgres-data", "mountPath": "/var/lib/postgresql/data"}]}}}, {"name": "redis", "version": "7-alpine", "build": {"image": "redis:7-alpine", "values": {"command": ["redis-server", "--appendonly", "yes"], "healthcheck": [{"name": "test", "value": ["CMD", "redis-cli", "ping"]}, {"name": "interval", "value": 30}, {"name": "timeout", "value": 10}, {"name": "retries", "value": 3}], "volumes": [{"name": "redis-data", "mountPath": "/data"}]}}}], "extend": []}}